@charset "UTF-8";
:root {
  --color-bg-01: #F5F2EF;
  --color-bg-02: #EDE8E4;
  --color-accent-01: #DCCFD8;
  --color-accent-02: #B9A0C9;
  --color-text-primary: #1A1A1A;
  --color-text-secondary: #4A4A4A;
  --color-text-light: #6B6B6B;
  --color-text-white: #FFFFFF;
  --color-white: #FFFFFF;
  --color-black: #000000;
  --color-transparent: transparent;
  --gradient-primary: linear-gradient(135deg, var(--color-accent-01) 0%, var(--color-accent-02) 100%);
  --gradient-overlay: linear-gradient(180deg, rgba(26, 26, 26, 0.6) 0%, rgba(26, 26, 26, 0.3) 100%);
}

:root {
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-heading: "Playfair Display", Georgia, serif;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

:root {
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  --space-40: 10rem;
  --space-48: 12rem;
  --space-56: 14rem;
  --space-64: 16rem;
}

:root {
  --container-max-width: 1200px;
  --container-padding: var(--space-4);
  --container-padding-lg: var(--space-6);
  --section-padding-y: var(--space-16);
  --section-padding-y-lg: var(--space-24);
  --grid-gap: var(--space-6);
  --grid-gap-lg: var(--space-8);
}

:root {
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
}

:root {
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

:root {
  --transition-fast: 150ms ease-in-out;
  --transition-base: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out-cubic: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-cubic: cubic-bezier(0.4, 0, 1, 1);
}

:root {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}

:root {
  --focus-ring: 0 0 0 3px rgba(185, 160, 201, 0.5);
  --focus-ring-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-base: 0ms;
    --transition-slow: 0ms;
  }
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}
body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-02);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

h1 {
  font-size: var(--font-size-4xl);
  font-style: italic;
  letter-spacing: var(--letter-spacing-tight);
}
@media (min-width: 768px) {
  h1 {
    font-size: var(--font-size-5xl);
  }
}
@media (min-width: 1024px) {
  h1 {
    font-size: var(--font-size-6xl);
  }
}

h2 {
  font-size: var(--font-size-3xl);
  font-style: italic;
}
@media (min-width: 768px) {
  h2 {
    font-size: var(--font-size-4xl);
  }
}

h3 {
  font-size: var(--font-size-2xl);
}
@media (min-width: 768px) {
  h3 {
    font-size: var(--font-size-3xl);
  }
}

h4 {
  font-size: var(--font-size-xl);
}
@media (min-width: 768px) {
  h4 {
    font-size: var(--font-size-2xl);
  }
}

h5 {
  font-size: var(--font-size-lg);
}
@media (min-width: 768px) {
  h5 {
    font-size: var(--font-size-xl);
  }
}

h6 {
  font-size: var(--font-size-base);
}
@media (min-width: 768px) {
  h6 {
    font-size: var(--font-size-lg);
  }
}

p {
  margin-bottom: var(--space-4);
  color: var(--color-text-secondary);
}
p:last-child {
  margin-bottom: 0;
}

a {
  color: var(--color-accent-02);
  text-decoration: none;
  transition: color var(--transition-fast);
}
a:hover, a:focus {
  color: var(--color-text-primary);
  text-decoration: underline;
}
a:focus {
  outline: none;
  box-shadow: var(--focus-ring);
  border-radius: var(--radius-sm);
}

ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-2);
  color: var(--color-text-secondary);
}
li:last-child {
  margin-bottom: 0;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

svg {
  display: block;
  max-width: 100%;
  height: auto;
}

button,
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}
button:focus {
  outline: none;
  box-shadow: var(--focus-ring);
  border-radius: var(--radius-sm);
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-text-primary);
  color: var(--color-white);
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--radius-base);
  z-index: var(--z-index-tooltip);
}
.skip-link:focus {
  top: 6px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}
@media (min-width: 1024px) {
  .container {
    padding: 0 var(--container-padding-lg);
  }
}

.section {
  padding: var(--section-padding-y) 0;
}
@media (min-width: 1024px) {
  .section {
    padding: var(--section-padding-y-lg) 0;
  }
}

.grid {
  display: grid;
  gap: var(--grid-gap);
}
@media (min-width: 1024px) {
  .grid {
    gap: var(--grid-gap-lg);
  }
}

.grid--2 {
  grid-template-columns: 1fr;
}
@media (min-width: 768px) {
  .grid--2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.grid--3 {
  grid-template-columns: 1fr;
}
@media (min-width: 768px) {
  .grid--3 {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .grid--3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

.grid--4 {
  grid-template-columns: 1fr;
}
@media (min-width: 640px) {
  .grid--4 {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .grid--4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.mb-12 {
  margin-bottom: var(--space-12);
}

.mt-0 {
  margin-top: 0;
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-8 {
  margin-top: var(--space-8);
}

.mt-12 {
  margin-top: var(--space-12);
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

@media (min-width: 768px) {
  .md\\:hidden {
    display: none;
  }
  .md\\:block {
    display: block;
  }
}
@media (min-width: 1024px) {
  .lg\\:hidden {
    display: none;
  }
  .lg\\:block {
    display: block;
  }
}
.logo {
  font-family: var(--font-heading);
  font-style: italic;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-wide);
  position: relative;
}
.logo::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}
@media (min-width: 768px) {
  .logo {
    font-size: var(--font-size-2xl);
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}
.btn:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}
.btn--primary {
  background: var(--gradient-primary);
  color: var(--color-white);
}
.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
.btn--primary:active {
  transform: translateY(0);
}
.btn--secondary {
  background: transparent;
  color: var(--color-accent-02);
  border-color: var(--color-accent-02);
}
.btn--secondary:hover {
  background: var(--color-accent-02);
  color: var(--color-white);
}
.btn--large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(245, 242, 239, 0.95);
  backdrop-filter: blur(10px);
  z-index: var(--z-index-sticky);
  transition: all var(--transition-base);
}

.nav__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--space-4) var(--container-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (min-width: 1024px) {
  .nav__container {
    padding: var(--space-4) var(--container-padding-lg);
  }
}
.nav__menu {
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-8);
}
@media (min-width: 768px) {
  .nav__menu {
    display: flex;
  }
}
.nav__item {
  margin: 0;
}
.nav__link {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  transition: color var(--transition-fast);
}
.nav__link:hover, .nav__link:focus {
  color: var(--color-accent-02);
  text-decoration: none;
}
.nav__toggle {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: var(--space-2);
}
@media (min-width: 768px) {
  .nav__toggle {
    display: none;
  }
}
.nav__toggle-line {
  width: 24px;
  height: 2px;
  background: var(--color-text-primary);
  transition: all var(--transition-fast);
}

.hero-landing {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--color-bg-01) 0%, var(--color-bg-02) 100%);
  position: relative;
  overflow: hidden;
  padding: var(--space-20) 0;
}
.hero-landing__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-16);
  align-items: center;
}
@media (min-width: 1024px) {
  .hero-landing__container {
    grid-template-columns: 1.2fr 1fr;
    gap: var(--space-20);
    padding: 0 var(--container-padding-lg);
  }
}
.hero-landing__content {
  text-align: center;
}
@media (min-width: 1024px) {
  .hero-landing__content {
    text-align: left;
  }
}
.hero-landing__text {
  margin-bottom: var(--space-12);
}
.hero-landing__title {
  margin-bottom: var(--space-6);
}
.hero-landing__title-main {
  display: block;
  font-family: var(--font-heading);
  font-style: italic;
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-medium);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-2);
}
@media (min-width: 768px) {
  .hero-landing__title-main {
    font-size: var(--font-size-6xl);
  }
}
.hero-landing__title-sub {
  display: block;
  font-family: var(--font-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-regular);
  color: var(--color-text-secondary);
}
@media (min-width: 768px) {
  .hero-landing__title-sub {
    font-size: var(--font-size-2xl);
  }
}
.hero-landing__description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-8);
  max-width: 500px;
}
@media (min-width: 1024px) {
  .hero-landing__description {
    margin-left: 0;
  }
}
@media (max-width: 1023px) {
  .hero-landing__description {
    margin-left: auto;
    margin-right: auto;
  }
}
.hero-landing__cta-group {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}
@media (min-width: 1024px) {
  .hero-landing__cta-group {
    justify-content: flex-start;
  }
}
.hero-landing__cta {
  min-width: 140px;
}
.hero-landing__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-6);
  margin-top: var(--space-8);
}
@media (min-width: 768px) {
  .hero-landing__stats {
    gap: var(--space-8);
  }
}
.hero-landing__visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media (min-width: 1024px) {
  .hero-landing__visual {
    justify-content: flex-end;
  }
}
.hero-landing__image-container {
  position: relative;
  width: 350px;
  height: 350px;
}
@media (min-width: 768px) {
  .hero-landing__image-container {
    width: 400px;
    height: 400px;
  }
}
@media (min-width: 1024px) {
  .hero-landing__image-container {
    width: 450px;
    height: 450px;
  }
}
.hero-landing__slideshow {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
}
.hero-landing__decoration {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}
.hero-landing__decoration--1 {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, var(--color-accent-01), var(--color-accent-02));
  top: -20px;
  right: -20px;
  opacity: 0.7;
  animation: float-1 6s ease-in-out infinite;
}
.hero-landing__decoration--2 {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-accent-02), var(--color-accent-01));
  bottom: -10px;
  left: -30px;
  opacity: 0.6;
  animation: float-2 8s ease-in-out infinite;
}
.hero-landing__decoration--3 {
  width: 40px;
  height: 40px;
  background: var(--color-accent-01);
  top: 50%;
  right: -50px;
  opacity: 0.5;
  animation: float-3 7s ease-in-out infinite;
}
.hero-landing__floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.stat-item {
  text-align: center;
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.stat-item__number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-02);
  margin-bottom: var(--space-1);
}
@media (min-width: 768px) {
  .stat-item__number {
    font-size: var(--font-size-3xl);
  }
}
.stat-item__label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.floating-element {
  position: absolute;
  font-size: var(--font-size-2xl);
  opacity: 0.6;
  animation-duration: 8s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}
.floating-element--1 {
  top: 20%;
  left: 10%;
  animation-name: float-gentle;
  animation-delay: 0s;
}
.floating-element--2 {
  top: 60%;
  right: 15%;
  animation-name: float-gentle;
  animation-delay: 2s;
}
.floating-element--3 {
  bottom: 30%;
  left: 5%;
  animation-name: float-gentle;
  animation-delay: 4s;
}
.floating-element--4 {
  top: 80%;
  right: 25%;
  animation-name: float-gentle;
  animation-delay: 6s;
}

.slideshow-container-landing {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-3xl);
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  border: 6px solid var(--color-white);
  background: var(--color-white);
  transform: rotate(-5deg);
  transition: transform var(--transition-base);
}
.slideshow-container-landing:hover {
  transform: rotate(0deg) scale(1.02);
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity var(--transition-slow);
}
.slide--active {
  opacity: 1;
}
.slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(1.1);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.slide--entering {
  animation: slideIn var(--transition-slow) ease-out;
}

@keyframes float-1 {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}
@keyframes float-2 {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(-180deg);
  }
}
@keyframes float-3 {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.1);
  }
}
@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}
.about {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-02);
}
.about__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
  align-items: center;
}
@media (min-width: 1024px) {
  .about__container {
    grid-template-columns: 1fr 1fr;
    padding: 0 var(--container-padding-lg);
  }
}
.about__content {
  order: 2;
}
@media (min-width: 1024px) {
  .about__content {
    order: 1;
  }
}
.about__title {
  text-align: center;
  margin-bottom: var(--space-8);
}
@media (min-width: 1024px) {
  .about__title {
    text-align: left;
  }
}
.about__bio {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
}
.about__image {
  order: 1;
  display: flex;
  justify-content: center;
}
@media (min-width: 1024px) {
  .about__image {
    order: 2;
    justify-content: flex-end;
  }
}
.about__portrait {
  width: 280px;
  height: 350px;
  object-fit: cover;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}
@media (min-width: 768px) {
  .about__portrait {
    width: 320px;
    height: 400px;
  }
}

.work {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-01);
}
.work__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}
@media (min-width: 1024px) {
  .work__container {
    padding: 0 var(--container-padding-lg);
  }
}
.work__title {
  text-align: center;
  margin-bottom: var(--space-12);
}
.work__tabs {
  display: flex;
  justify-content: center;
  gap: var(--space-2);
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}
@media (min-width: 768px) {
  .work__tabs {
    gap: var(--space-4);
  }
}
.work__tab {
  padding: var(--space-3) var(--space-6);
  background: transparent;
  color: var(--color-text-secondary);
  border: 2px solid var(--color-accent-01);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-base);
  cursor: pointer;
}
.work__tab:hover {
  background: var(--color-accent-01);
  color: var(--color-text-primary);
}
.work__tab--active {
  background: var(--gradient-primary);
  color: var(--color-white);
  border-color: transparent;
}
.work__panels {
  position: relative;
}
.work__panel {
  display: none;
}
.work__panel--active {
  display: block;
}
.work__gallery {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}
@media (min-width: 640px) {
  .work__gallery {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .work__gallery {
    grid-template-columns: repeat(3, 1fr);
  }
}
.work__item {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
}
.work__item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}
.work__item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
}
.work__item--video .phone-mockup {
  position: relative;
  background: var(--color-text-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-4);
}
.work__item--video .phone-mockup img {
  border-radius: var(--radius-lg);
}
.work__item--video .phone-mockup .play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-base);
}
.work__item--video .phone-mockup .play-button::after {
  content: "";
  width: 0;
  height: 0;
  border-left: 20px solid var(--color-text-primary);
  border-top: 12px solid transparent;
  border-bottom: 12px solid transparent;
  margin-left: 4px;
}
.work__item--video .phone-mockup .play-button:hover {
  background: var(--color-white);
  transform: translate(-50%, -50%) scale(1.1);
}

.services {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-02);
}
.services__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}
@media (min-width: 1024px) {
  .services__container {
    padding: 0 var(--container-padding-lg);
  }
}
.services__title {
  text-align: center;
  margin-bottom: var(--space-12);
}
.services__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}
@media (min-width: 768px) {
  .services__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .services__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.service-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}
.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}
.service-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}
.service-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}
.service-card__title {
  font-size: var(--font-size-xl);
  margin-bottom: 0;
}
.service-card__icon {
  font-size: var(--font-size-3xl);
  opacity: 0.8;
}
.service-card__description {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-6);
  line-height: var(--line-height-relaxed);
}
.service-card__features {
  list-style: none;
  padding: 0;
  margin-bottom: var(--space-8);
}
.service-card__features li {
  position: relative;
  padding-left: var(--space-6);
  margin-bottom: var(--space-3);
  color: var(--color-text-secondary);
}
.service-card__features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--color-accent-02);
  font-weight: var(--font-weight-bold);
}
.service-card__price {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-accent-02);
  text-align: center;
  padding: var(--space-4);
  background: var(--color-bg-01);
  border-radius: var(--radius-lg);
}

.testimonials {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-01);
}
.testimonials__container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--container-padding);
  text-align: center;
}
@media (min-width: 1024px) {
  .testimonials__container {
    padding: 0 var(--container-padding-lg);
  }
}
.testimonials__title {
  margin-bottom: var(--space-12);
}
.testimonials__carousel {
  position: relative;
  margin-bottom: var(--space-8);
}
.testimonials__controls {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
}
.testimonials__dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--color-accent-01);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}
.testimonials__dot:hover, .testimonials__dot--active {
  background: var(--color-accent-02);
  transform: scale(1.2);
}

.testimonial {
  display: none;
}
.testimonial--active {
  display: block;
}
.testimonial__quote {
  font-size: var(--font-size-xl);
  font-style: italic;
  line-height: var(--line-height-relaxed);
  color: var(--color-text-primary);
  margin-bottom: var(--space-6);
}
@media (min-width: 768px) {
  .testimonial__quote {
    font-size: var(--font-size-2xl);
  }
}
.testimonial__quote::before, .testimonial__quote::after {
  content: '"';
  color: var(--color-accent-02);
  font-size: 1.5em;
}
.testimonial__author {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.contact {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-02);
}
.contact__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}
@media (min-width: 1024px) {
  .contact__container {
    padding: 0 var(--container-padding-lg);
  }
}
.contact__title {
  text-align: center;
  margin-bottom: var(--space-12);
}
.contact__content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-12);
}
@media (min-width: 1024px) {
  .contact__content {
    grid-template-columns: 1fr 2fr;
  }
}
.contact__info {
  text-align: center;
}
@media (min-width: 1024px) {
  .contact__info {
    text-align: left;
  }
}
.contact__description {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-8);
}
.contact__social {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
}
@media (min-width: 1024px) {
  .contact__social {
    justify-content: flex-start;
  }
}
.contact__form {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--space-2);
  box-shadow: var(--shadow-lg);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: 50%;
  color: var(--color-white);
  transition: all var(--transition-base);
}
.social-link:hover {
  transform: translateY(-2px) scale(1.1);
  box-shadow: var(--shadow-lg);
}
.social-link .social-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
}

.footer {
  background: var(--color-text-primary);
  color: var(--color-white);
  padding: var(--space-8) 0;
}
.footer__container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}
@media (min-width: 1024px) {
  .footer__container {
    padding: 0 var(--container-padding-lg);
  }
}
.footer__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
}
@media (min-width: 768px) {
  .footer__content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}
.footer__copyright {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0;
}
.footer__nav {
  display: flex;
  gap: var(--space-6);
}
.footer__link {
  color: rgba(255, 255, 255, 0.8);
  transition: color var(--transition-fast);
}
.footer__link:hover, .footer__link:focus {
  color: var(--color-white);
  text-decoration: none;
}

.typeform-embed {
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.music-player {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: var(--z-index-modal);
}
.music-player__container {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  transition: all var(--transition-base);
}
.music-player__controls {
  padding: var(--space-4);
}
.music-player__toggle {
  width: 56px;
  height: 56px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-base);
}
.music-player__toggle:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}
.music-player__icon {
  width: 24px;
  height: 24px;
  fill: var(--color-white);
  transition: opacity var(--transition-fast);
}
.music-player__icon--close {
  position: absolute;
  opacity: 0;
}
.music-player__content {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-base);
}
.music-player--open .music-player__content {
  max-height: 400px;
}
.music-player__header {
  padding: var(--space-4) var(--space-6) var(--space-2);
  border-bottom: 1px solid var(--color-bg-02);
}
.music-player__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-1);
}
.music-player__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: 0;
}
.music-player__embed {
  width: 320px;
  height: 180px;
}
.music-player__embed iframe {
  width: 100%;
  height: 100%;
  border: none;
}
.music-player--open .music-player__icon--music {
  opacity: 0;
}
.music-player--open .music-player__icon--close {
  opacity: 1;
}
@media (max-width: 767px) {
  .music-player {
    bottom: var(--space-4);
    right: var(--space-4);
  }
  .music-player__embed {
    width: 280px;
    height: 160px;
  }
}

@media (max-width: 767px) {
  .nav__menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--color-bg-01);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-8);
    transform: translateX(-100%);
    transition: transform var(--transition-base);
    z-index: var(--z-index-modal);
  }
  .nav__menu--open {
    transform: translateX(0);
  }
}

.nav__link--active {
  color: var(--color-accent-02);
  position: relative;
}
.nav__link--active::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--color-accent-02);
  border-radius: var(--radius-full);
}

.nav__toggle--open .nav__toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}
.nav__toggle--open .nav__toggle-line:nth-child(2) {
  opacity: 0;
}
.nav__toggle--open .nav__toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.nav-open {
  overflow: hidden;
}
@media (max-width: 767px) {
  .nav-open .header {
    background: var(--color-bg-01);
  }
}

.skills-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-top: var(--space-6);
}
@media (min-width: 768px) {
  .skills-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.skill-category h4 {
  color: var(--color-accent-02);
  margin-bottom: var(--space-4);
  font-size: var(--font-size-lg);
}
.skill-category ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.skill-category ul li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--color-bg-01);
  color: var(--color-text-secondary);
}
.skill-category ul li:last-child {
  border-bottom: none;
}

.experience {
  background: var(--color-bg-01);
}
.experience__title {
  text-align: center;
  margin-bottom: var(--space-12);
}
.experience__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}
@media (min-width: 768px) {
  .experience__grid {
    gap: var(--space-12);
  }
}
.experience__item {
  position: relative;
  padding: var(--space-6);
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}
.experience__item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}
.experience__year {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-accent-02);
  margin-bottom: var(--space-2);
}
.experience__role {
  margin-bottom: var(--space-1);
  font-size: var(--font-size-xl);
}
.experience__company {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-3);
}
.experience__description {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.additional-services {
  margin-top: var(--space-16);
  padding-top: var(--space-16);
  border-top: 1px solid var(--color-bg-01);
}
.additional-services__title {
  text-align: center;
  margin-bottom: var(--space-8);
}
.additional-services__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}
@media (min-width: 768px) {
  .additional-services__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.additional-service {
  text-align: center;
  padding: var(--space-6);
  background: var(--color-bg-01);
  border-radius: var(--radius-xl);
}
.additional-service h3 {
  margin-bottom: var(--space-3);
  color: var(--color-accent-02);
}
.additional-service p {
  margin-bottom: var(--space-4);
  font-size: var(--font-size-sm);
}
.additional-service__price {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.process {
  margin-top: var(--space-16);
}
.process__title {
  text-align: center;
  margin-bottom: var(--space-12);
}
.process__steps {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
}
@media (min-width: 768px) {
  .process__steps {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1024px) {
  .process__steps {
    grid-template-columns: repeat(4, 1fr);
  }
}
.process__step {
  text-align: center;
  position: relative;
}
.process__number {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  color: var(--color-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin: 0 auto var(--space-4);
}
.process__step-title {
  margin-bottom: var(--space-3);
  font-size: var(--font-size-lg);
}
.process__step-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.faq {
  margin-top: var(--space-16);
}
.faq__title {
  text-align: center;
  margin-bottom: var(--space-12);
}
.faq__items {
  max-width: 800px;
  margin: 0 auto;
}
.faq__item {
  margin-bottom: var(--space-6);
  padding: var(--space-6);
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}
.faq__question {
  margin-bottom: var(--space-3);
  color: var(--color-accent-02);
  font-size: var(--font-size-lg);
}
.faq__answer {
  color: var(--color-text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.contact__methods {
  margin: var(--space-8) 0;
}

.contact__method {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}
.contact__method:last-child {
  margin-bottom: 0;
}
.contact__method-icon {
  font-size: var(--font-size-2xl);
  flex-shrink: 0;
}
.contact__method-content h3 {
  margin-bottom: var(--space-2);
  font-size: var(--font-size-lg);
  color: var(--color-accent-02);
}
.contact__method-content p, .contact__method-content a {
  color: var(--color-text-secondary);
  margin-bottom: 0;
}
.contact__method-content a {
  text-decoration: underline;
}
.contact__method-content a:hover {
  color: var(--color-accent-02);
}

.contact-form .form-group {
  margin-bottom: var(--space-6);
}
.contact-form label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}
.contact-form input,
.contact-form select,
.contact-form textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--color-bg-02);
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}
.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: var(--color-accent-02);
}
.contact-form textarea {
  resize: vertical;
  min-height: 120px;
}
.contact-form .btn {
  width: 100%;
}
@media (min-width: 768px) {
  .contact-form .btn {
    width: auto;
  }
}

.service-card--featured {
  position: relative;
  border: 2px solid var(--color-accent-02);
  transform: scale(1.05);
}
@media (max-width: 767px) {
  .service-card--featured {
    transform: none;
  }
}
.service-card__badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-accent-02);
  color: var(--color-white);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}
.service-card__cta {
  width: 100%;
  margin-top: var(--space-4);
}

.work__item-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: var(--color-white);
  padding: var(--space-6) var(--space-4) var(--space-4);
  transform: translateY(100%);
  transition: transform var(--transition-base);
}
.work__item:hover .work__item-overlay {
  transform: translateY(0);
}
.work__item-title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-2);
  color: var(--color-white);
}
.work__item-description {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
}

.about__image-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: var(--gradient-primary);
  border-radius: 50%;
  opacity: 0.2;
  z-index: -1;
}

html {
  scroll-padding-top: 80px;
}

.loading {
  opacity: 0.5;
  pointer-events: none;
  transition: opacity var(--transition-base);
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--transition-slow);
}
.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.lazy-image {
  background: var(--color-bg-01);
  position: relative;
  overflow: hidden;
}
.lazy-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}
.lazy-image img {
  transition: opacity var(--transition-base);
}
.lazy-image img.loaded {
  opacity: 1;
}
.lazy-image img:not(.loaded) {
  opacity: 0;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
@media print {
  .header,
  .footer,
  .btn,
  .nav__toggle {
    display: none !important;
  }
  .hero {
    min-height: auto;
    page-break-after: always;
  }
  .section {
    page-break-inside: avoid;
  }
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: #fff;
  }
  h1, h2, h3 {
    page-break-after: avoid;
  }
}

/*# sourceMappingURL=main.css.map */
