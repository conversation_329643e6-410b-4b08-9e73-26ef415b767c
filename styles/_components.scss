// =============================================================================
// COMPONENTS - BEM Methodology
// =============================================================================

// Logo Component
// -----------------------------------------------------------------------------
.logo {
  font-family: var(--font-heading);
  font-style: italic;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  letter-spacing: var(--letter-spacing-wide);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
  }
  
  @include media-up('md') {
    font-size: var(--font-size-2xl);
  }
}

// Button Components
// -----------------------------------------------------------------------------
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  
  &:focus {
    outline: none;
    box-shadow: var(--focus-ring);
  }
  
  &--primary {
    background: var(--gradient-primary);
    color: var(--color-white);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  &--secondary {
    background: transparent;
    color: var(--color-accent-02);
    border-color: var(--color-accent-02);
    
    &:hover {
      background: var(--color-accent-02);
      color: var(--color-white);
    }
  }
  
  &--large {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
  }
}

// Header & Navigation
// -----------------------------------------------------------------------------
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(245, 242, 239, 0.95);
  backdrop-filter: blur(10px);
  z-index: var(--z-index-sticky);
  transition: all var(--transition-base);
}

.nav {
  &__container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: var(--space-4) var(--container-padding);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    @include media-up('lg') {
      padding: var(--space-4) var(--container-padding-lg);
    }
  }
  
  &__menu {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-8);
    
    @include media-up('md') {
      display: flex;
    }
  }
  
  &__item {
    margin: 0;
  }
  
  &__link {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    transition: color var(--transition-fast);
    
    &:hover,
    &:focus {
      color: var(--color-accent-02);
      text-decoration: none;
    }
  }
  
  &__toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: var(--space-2);
    
    @include media-up('md') {
      display: none;
    }
  }
  
  &__toggle-line {
    width: 24px;
    height: 2px;
    background: var(--color-text-primary);
    transition: all var(--transition-fast);
  }
}

// Hero Section
// -----------------------------------------------------------------------------
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: var(--color-bg-01);
  position: relative;
  overflow: hidden;
  
  &__container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: var(--space-20) var(--container-padding);
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);
    align-items: center;
    
    @include media-up('lg') {
      grid-template-columns: 1fr 1fr;
      padding: var(--space-20) var(--container-padding-lg);
    }
  }
  
  &__content {
    text-align: center;
    
    @include media-up('lg') {
      text-align: left;
    }
  }
  
  &__title {
    margin-bottom: var(--space-6);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  &__subtitle {
    font-size: var(--font-size-lg);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-8);
    
    @include media-up('md') {
      font-size: var(--font-size-xl);
    }
  }
  
  &__cta {
    margin-bottom: var(--space-8);
  }
  
  &__visual {
    position: relative;
    height: 400px;
    
    @include media-up('lg') {
      height: 500px;
    }
  }
  
  &__slideshow {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;

    @include media-up('lg') {
      width: 400px;
      height: 400px;
    }
  }
}

// Hero Combined Section (Best of Both Designs)
// -----------------------------------------------------------------------------
.hero-combined {
  min-height: 100vh;
  background: var(--color-bg-02);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // Full-Width Overlapping Typography
  &__typography {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 20;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__title {
    margin: 0;
    position: relative;
    width: 100vw;
    text-align: center;
  }

  &__line {
    display: block;
    font-family: var(--font-heading);
    font-style: italic;
    font-weight: var(--font-weight-semibold);
    color: var(--color-white);
    text-shadow:
      3px 3px 6px rgba(0, 0, 0, 0.8),
      0 0 12px rgba(0, 0, 0, 0.6),
      0 0 24px rgba(0, 0, 0, 0.4);
    line-height: 0.8;
    position: relative;

    // Large responsive font sizing for full-width impact
    font-size: clamp(3rem, 12vw, 8rem);

    @include media-up('md') {
      font-size: clamp(4rem, 12vw, 10rem);
    }

    @include media-up('lg') {
      font-size: clamp(5rem, 12vw, 12rem);
    }

    // Staggered positioning across full width for "Daja Lucas"
    &--1 { // Daja
      transform: translateX(-20vw);
      margin-bottom: -0.2em;
    }

    &--2 { // Lucas
      transform: translateX(15vw);
    }
  }

  // Centered Container
  &__container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-20) var(--container-padding);
    position: relative;
    z-index: 10;

    @include media-up('lg') {
      padding: var(--space-20) var(--container-padding-lg);
    }
  }

  // Arch Wrapper (includes button)
  &__arch-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-8);
  }

  // Welcome Text
  &__welcome {
    text-align: center;
    margin-bottom: var(--space-4);
  }

  &__welcome-text {
    font-family: var(--font-heading);
    font-style: italic;
    font-size: var(--font-size-3xl);
    color: var(--color-white);
    text-shadow:
      2px 2px 4px rgba(0, 0, 0, 0.8),
      0 0 8px rgba(0, 0, 0, 0.6);
    margin: 0;
    opacity: 0.95;
    font-weight: var(--font-weight-semibold);

    @include media-up('md') {
      font-size: var(--font-size-4xl);
    }

    @include media-up('lg') {
      font-size: var(--font-size-5xl);
    }
  }

  // CTA Button
  &__cta {
    display: flex;
    justify-content: center;
  }

  &__connect-btn {
    background: var(--gradient-primary);
    border: none;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--color-white);
    text-decoration: none;
    transition: all var(--transition-base);
    box-shadow: var(--shadow-lg);

    &:hover,
    &:focus {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
      text-decoration: none;
    }
  }

  &__arch {
    position: relative;
    width: 350px;
    height: 450px;

    @include media-up('md') {
      width: 400px;
      height: 520px;
    }

    @include media-up('lg') {
      width: 450px;
      height: 580px;
    }

    // Create larger arched doorway shape
    background: var(--color-white);
    border-radius: 50% 50% 0 0;
    overflow: hidden;
    box-shadow:
      0 0 0 6px var(--color-text-primary),
      inset 0 6px 16px rgba(0, 0, 0, 0.15),
      0 12px 40px rgba(0, 0, 0, 0.15);

    // Add depth with pseudo-element
    &::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      background: linear-gradient(145deg, var(--color-accent-01), var(--color-accent-02));
      border-radius: 50% 50% 0 0;
      z-index: -1;
    }
  }

  &__slideshow {
    width: 100%;
    height: 100%;
    position: relative;
  }
}

// Hero Slideshow
// -----------------------------------------------------------------------------
.slideshow-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  border: 4px solid var(--color-white);
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity var(--transition-slow);

  &--active {
    opacity: 1;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}



// Slideshow animations
@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(1.1);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.slide--entering {
  animation: slideIn var(--transition-slow) ease-out;
}

// About Section
// -----------------------------------------------------------------------------
.about {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-02);
  
  &__container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);
    align-items: center;
    
    @include media-up('lg') {
      grid-template-columns: 1fr 1fr;
      padding: 0 var(--container-padding-lg);
    }
  }
  
  &__content {
    order: 2;
    
    @include media-up('lg') {
      order: 1;
    }
  }
  
  &__title {
    text-align: center;
    margin-bottom: var(--space-8);
    
    @include media-up('lg') {
      text-align: left;
    }
  }
  
  &__bio {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
  }
  
  &__image {
    order: 1;
    display: flex;
    justify-content: center;
    
    @include media-up('lg') {
      order: 2;
      justify-content: flex-end;
    }
  }
  
  &__portrait {
    width: 280px;
    height: 350px;
    object-fit: cover;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    
    @include media-up('md') {
      width: 320px;
      height: 400px;
    }
  }
}

// Work Section
// -----------------------------------------------------------------------------
.work {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-01);
  
  &__container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
    
    @include media-up('lg') {
      padding: 0 var(--container-padding-lg);
    }
  }
  
  &__title {
    text-align: center;
    margin-bottom: var(--space-12);
  }
  
  &__tabs {
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    margin-bottom: var(--space-12);
    flex-wrap: wrap;
    
    @include media-up('md') {
      gap: var(--space-4);
    }
  }
  
  &__tab {
    padding: var(--space-3) var(--space-6);
    background: transparent;
    color: var(--color-text-secondary);
    border: 2px solid var(--color-accent-01);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-base);
    cursor: pointer;
    
    &:hover {
      background: var(--color-accent-01);
      color: var(--color-text-primary);
    }
    
    &--active {
      background: var(--gradient-primary);
      color: var(--color-white);
      border-color: transparent;
    }
  }
  
  &__panels {
    position: relative;
  }
  
  &__panel {
    display: none;
    
    &--active {
      display: block;
    }
  }
  
  &__gallery {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
    
    @include media-up('sm') {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include media-up('lg') {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  &__item {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
    }
    
    img {
      width: 100%;
      height: 250px;
      object-fit: cover;
    }
    
    &--video {
      .phone-mockup {
        position: relative;
        background: var(--color-text-primary);
        border-radius: var(--radius-2xl);
        padding: var(--space-4);
        
        img {
          border-radius: var(--radius-lg);
        }
        
        .play-button {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all var(--transition-base);
          
          &::after {
            content: '';
            width: 0;
            height: 0;
            border-left: 20px solid var(--color-text-primary);
            border-top: 12px solid transparent;
            border-bottom: 12px solid transparent;
            margin-left: 4px;
          }
          
          &:hover {
            background: var(--color-white);
            transform: translate(-50%, -50%) scale(1.1);
          }
        }
      }
    }
  }
}

// Services Section
// -----------------------------------------------------------------------------
.services {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-02);

  &__container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);

    @include media-up('lg') {
      padding: 0 var(--container-padding-lg);
    }
  }

  &__title {
    text-align: center;
    margin-bottom: var(--space-12);
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);

    @include media-up('md') {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-up('lg') {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.service-card {
  background: var(--color-white);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-6);
  }

  &__title {
    font-size: var(--font-size-xl);
    margin-bottom: 0;
  }

  &__icon {
    font-size: var(--font-size-3xl);
    opacity: 0.8;
  }

  &__description {
    color: var(--color-text-secondary);
    margin-bottom: var(--space-6);
    line-height: var(--line-height-relaxed);
  }

  &__features {
    list-style: none;
    padding: 0;
    margin-bottom: var(--space-8);

    li {
      position: relative;
      padding-left: var(--space-6);
      margin-bottom: var(--space-3);
      color: var(--color-text-secondary);

      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: var(--color-accent-02);
        font-weight: var(--font-weight-bold);
      }
    }
  }

  &__price {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-accent-02);
    text-align: center;
    padding: var(--space-4);
    background: var(--color-bg-01);
    border-radius: var(--radius-lg);
  }
}

// Testimonials Section
// -----------------------------------------------------------------------------
.testimonials {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-01);

  &__container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--container-padding);
    text-align: center;

    @include media-up('lg') {
      padding: 0 var(--container-padding-lg);
    }
  }

  &__title {
    margin-bottom: var(--space-12);
  }

  &__carousel {
    position: relative;
    margin-bottom: var(--space-8);
  }

  &__controls {
    display: flex;
    justify-content: center;
    gap: var(--space-3);
  }

  &__dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--color-accent-01);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);

    &:hover,
    &--active {
      background: var(--color-accent-02);
      transform: scale(1.2);
    }
  }
}

.testimonial {
  display: none;

  &--active {
    display: block;
  }

  &__quote {
    font-size: var(--font-size-xl);
    font-style: italic;
    line-height: var(--line-height-relaxed);
    color: var(--color-text-primary);
    margin-bottom: var(--space-6);

    @include media-up('md') {
      font-size: var(--font-size-2xl);
    }

    &::before,
    &::after {
      content: '"';
      color: var(--color-accent-02);
      font-size: 1.5em;
    }
  }

  &__author {
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
  }
}

// Contact Section
// -----------------------------------------------------------------------------
.contact {
  padding: var(--section-padding-y) 0;
  background: var(--color-bg-02);

  &__container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);

    @include media-up('lg') {
      padding: 0 var(--container-padding-lg);
    }
  }

  &__title {
    text-align: center;
    margin-bottom: var(--space-12);
  }

  &__content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-12);

    @include media-up('lg') {
      grid-template-columns: 1fr 2fr;
    }
  }

  &__info {
    text-align: center;

    @include media-up('lg') {
      text-align: left;
    }
  }

  &__description {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-8);
  }

  &__social {
    display: flex;
    justify-content: center;
    gap: var(--space-4);

    @include media-up('lg') {
      justify-content: flex-start;
    }
  }

  &__form {
    background: var(--color-white);
    border-radius: var(--radius-2xl);
    padding: var(--space-2);
    box-shadow: var(--shadow-lg);
  }
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: 50%;
  color: var(--color-white);
  transition: all var(--transition-base);

  &:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: var(--shadow-lg);
  }

  .social-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
  }
}

// Footer
// -----------------------------------------------------------------------------
.footer {
  background: var(--color-text-primary);
  color: var(--color-white);
  padding: var(--space-8) 0;

  &__container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);

    @include media-up('lg') {
      padding: 0 var(--container-padding-lg);
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
    text-align: center;

    @include media-up('md') {
      flex-direction: row;
      justify-content: space-between;
      text-align: left;
    }
  }

  &__copyright {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
  }

  &__nav {
    display: flex;
    gap: var(--space-6);
  }

  &__link {
    color: rgba(255, 255, 255, 0.8);
    transition: color var(--transition-fast);

    &:hover,
    &:focus {
      color: var(--color-white);
      text-decoration: none;
    }
  }
}

// Typeform Embed
// -----------------------------------------------------------------------------
.typeform-embed {
  border-radius: var(--radius-xl);
  overflow: hidden;
}

// Music Player
// -----------------------------------------------------------------------------
.music-player {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: var(--z-index-modal);

  &__container {
    background: var(--color-white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: all var(--transition-base);
  }

  &__controls {
    padding: var(--space-4);
  }

  &__toggle {
    width: 56px;
    height: 56px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);

    &:hover {
      transform: scale(1.05);
      box-shadow: var(--shadow-lg);
    }
  }

  &__icon {
    width: 24px;
    height: 24px;
    fill: var(--color-white);
    transition: opacity var(--transition-fast);

    &--close {
      position: absolute;
      opacity: 0;
    }
  }

  &__content {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-base);

    .music-player--open & {
      max-height: 400px;
    }
  }

  &__header {
    padding: var(--space-4) var(--space-6) var(--space-2);
    border-bottom: 1px solid var(--color-bg-02);
  }

  &__title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-1);
  }

  &__subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-bottom: 0;
  }

  &__embed {
    width: 320px;
    height: 180px;

    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }

  // Open state
  &--open {
    .music-player__icon--music {
      opacity: 0;
    }

    .music-player__icon--close {
      opacity: 1;
    }
  }

  // Mobile adjustments
  @include media-down('md') {
    bottom: var(--space-4);
    right: var(--space-4);

    &__embed {
      width: 280px;
      height: 160px;
    }
  }
}

// Mobile Navigation
// -----------------------------------------------------------------------------
.nav__menu {
  @include media-down('md') {
    position: fixed;
    top: 0;
    right: 0;
    width: 60%;
    height: 100vh;
    background: rgba(245, 242, 239, 0.98);
    backdrop-filter: blur(15px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--space-10);
    transform: translateX(100%);
    transition: transform var(--transition-base);
    z-index: var(--z-index-modal);
    border-left: 3px solid var(--color-white);
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);

    &--open {
      transform: translateX(0);
    }
  }
}

.nav__link {
  @include media-down('md') {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    text-decoration: none;
    padding: var(--space-4) var(--space-6);
    border-bottom: 2px solid var(--color-white);
    width: 80%;
    text-align: center;
    transition: all var(--transition-base);

    &:hover,
    &:focus {
      color: var(--color-accent-02);
      background: rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
    }

    &:last-child {
      border-bottom: none;
    }
  }

  &--active {
    color: var(--color-accent-02);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      width: 100%;
      height: 2px;
      background: var(--color-accent-02);
      border-radius: var(--radius-full);
    }
  }
}

.nav__toggle {
  position: relative;
  z-index: calc(var(--z-index-modal) + 1);

  &--open {
    .nav__toggle-line {
      &:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
      }

      &:nth-child(2) {
        opacity: 0;
      }

      &:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
      }
    }
  }
}

// Body scroll lock when nav is open
.nav-open {
  overflow: hidden;

  @include media-down('md') {
    .header {
      background: rgba(245, 242, 239, 0.95);
      backdrop-filter: blur(10px);
    }
  }
}

// Skills Grid (About Page)
// -----------------------------------------------------------------------------
.skills-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  margin-top: var(--space-6);

  @include media-up('md') {
    grid-template-columns: repeat(3, 1fr);
  }
}

.skill-category {
  h4 {
    color: var(--color-accent-02);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-lg);
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: var(--space-2) 0;
      border-bottom: 1px solid var(--color-bg-01);
      color: var(--color-text-secondary);

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// Experience Timeline (About Page)
// -----------------------------------------------------------------------------
.experience {
  background: var(--color-bg-01);

  &__title {
    text-align: center;
    margin-bottom: var(--space-12);
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);

    @include media-up('md') {
      gap: var(--space-12);
    }
  }

  &__item {
    position: relative;
    padding: var(--space-6);
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: var(--gradient-primary);
      border-radius: var(--radius-full);
    }
  }

  &__year {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-accent-02);
    margin-bottom: var(--space-2);
  }

  &__role {
    margin-bottom: var(--space-1);
    font-size: var(--font-size-xl);
  }

  &__company {
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--space-3);
  }

  &__description {
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: 0;
  }
}

// Additional Services (Services Page)
// -----------------------------------------------------------------------------
.additional-services {
  margin-top: var(--space-16);
  padding-top: var(--space-16);
  border-top: 1px solid var(--color-bg-01);

  &__title {
    text-align: center;
    margin-bottom: var(--space-8);
  }

  &__grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);

    @include media-up('md') {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.additional-service {
  text-align: center;
  padding: var(--space-6);
  background: var(--color-bg-01);
  border-radius: var(--radius-xl);

  h3 {
    margin-bottom: var(--space-3);
    color: var(--color-accent-02);
  }

  p {
    margin-bottom: var(--space-4);
    font-size: var(--font-size-sm);
  }

  &__price {
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
  }
}

// Process Steps (Services Page)
// -----------------------------------------------------------------------------
.process {
  margin-top: var(--space-16);

  &__title {
    text-align: center;
    margin-bottom: var(--space-12);
  }

  &__steps {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-8);

    @include media-up('md') {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-up('lg') {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__step {
    text-align: center;
    position: relative;
  }

  &__number {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    color: var(--color-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    margin: 0 auto var(--space-4);
  }

  &__step-title {
    margin-bottom: var(--space-3);
    font-size: var(--font-size-lg);
  }

  &__step-description {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
  }
}

// FAQ Section (Services Page)
// -----------------------------------------------------------------------------
.faq {
  margin-top: var(--space-16);

  &__title {
    text-align: center;
    margin-bottom: var(--space-12);
  }

  &__items {
    max-width: 800px;
    margin: 0 auto;
  }

  &__item {
    margin-bottom: var(--space-6);
    padding: var(--space-6);
    background: var(--color-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
  }

  &__question {
    margin-bottom: var(--space-3);
    color: var(--color-accent-02);
    font-size: var(--font-size-lg);
  }

  &__answer {
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: 0;
  }
}

// Contact Methods (Contact Page)
// -----------------------------------------------------------------------------
.contact__methods {
  margin: var(--space-8) 0;
}

.contact__method {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-6);

  &:last-child {
    margin-bottom: 0;
  }

  &-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
  }

  &-content {
    h3 {
      margin-bottom: var(--space-2);
      font-size: var(--font-size-lg);
      color: var(--color-accent-02);
    }

    p, a {
      color: var(--color-text-secondary);
      margin-bottom: 0;
    }

    a {
      text-decoration: underline;

      &:hover {
        color: var(--color-accent-02);
      }
    }
  }
}

// Contact Form
// -----------------------------------------------------------------------------
.contact-form {
  .form-group {
    margin-bottom: var(--space-6);
  }

  label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
  }

  input,
  select,
  textarea {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--color-bg-02);
    border-radius: var(--radius-lg);
    font-family: inherit;
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);

    &:focus {
      outline: none;
      border-color: var(--color-accent-02);
    }
  }

  textarea {
    resize: vertical;
    min-height: 120px;
  }

  .btn {
    width: 100%;

    @include media-up('md') {
      width: auto;
    }
  }
}

// Service Card Enhancements
// -----------------------------------------------------------------------------
.service-card {
  &--featured {
    position: relative;
    border: 2px solid var(--color-accent-02);
    transform: scale(1.05);

    @include media-down('md') {
      transform: none;
    }
  }

  &__badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-accent-02);
    color: var(--color-white);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
  }

  &__cta {
    width: 100%;
    margin-top: var(--space-4);
  }
}

// Work Item Overlays
// -----------------------------------------------------------------------------
.work__item {
  &-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--color-white);
    padding: var(--space-6) var(--space-4) var(--space-4);
    transform: translateY(100%);
    transition: transform var(--transition-base);
  }

  &:hover &-overlay {
    transform: translateY(0);
  }

  &-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-2);
    color: var(--color-white);
  }

  &-description {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
  }
}

// About Page Image Decoration
// -----------------------------------------------------------------------------
.about__image-decoration {
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: var(--gradient-primary);
  border-radius: 50%;
  opacity: 0.2;
  z-index: -1;
}

// Hero Arch Animations
// -----------------------------------------------------------------------------
@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}
