// =============================================================================
// MAIN JAVASCRIPT - <PERSON><PERSON>
// =============================================================================

(function() {
  'use strict';

  // DOM Elements
  const header = document.querySelector('.header');
  const navToggle = document.querySelector('.nav__toggle');
  const navMenu = document.querySelector('.nav__menu');
  const navLinks = document.querySelectorAll('.nav__link');
  const sections = document.querySelectorAll('section[id]');
  const fadeElements = document.querySelectorAll('.fade-in');

  // Initialize the application
  function init() {
    setupNavigation();
    setupScrollEffects();
    setupLazyLoading();
    setupIntersectionObserver();
    setupTestimonialCarousel();
    setupMusicPlayer();
    setupHeroSlideshow();
  }

  // Navigation Setup
  function setupNavigation() {
    // Mobile menu toggle
    if (navToggle && navMenu) {
      navToggle.addEventListener('click', toggleMobileMenu);
    }

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
      link.addEventListener('click', closeMobileMenu);
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!header.contains(e.target)) {
        closeMobileMenu();
      }
    });

    // Note: Active navigation is now handled by server-side or page-specific logic
  }

  // Toggle mobile menu
  function toggleMobileMenu() {
    const isOpen = navMenu.classList.contains('nav__menu--open');

    if (isOpen) {
      closeMobileMenu();
    } else {
      openMobileMenu();
    }
  }

  // Open mobile menu
  function openMobileMenu() {
    navMenu.classList.add('nav__menu--open');
    navToggle.classList.add('nav__toggle--open');
    document.body.classList.add('nav-open');
  }

  // Close mobile menu
  function closeMobileMenu() {
    navMenu.classList.remove('nav__menu--open');
    navToggle.classList.remove('nav__toggle--open');
    document.body.classList.remove('nav-open');
  }



  // Scroll Effects Setup
  function setupScrollEffects() {
    window.addEventListener('scroll', throttle(handleScroll, 16));
  }

  // Handle scroll events
  function handleScroll() {
    const scrollY = window.scrollY;

    // Header background opacity
    if (header) {
      if (scrollY > 50) {
        header.classList.add('header--scrolled');
      } else {
        header.classList.remove('header--scrolled');
      }
    }

    // Parallax effect for hero section
    const hero = document.querySelector('.hero');
    if (hero) {
      const heroHeight = hero.offsetHeight;
      const scrollPercent = Math.min(scrollY / heroHeight, 1);
      const bgElement = hero.querySelector('.hero__bg-element');
      
      if (bgElement) {
        bgElement.style.transform = `translate(-50%, -50%) translateY(${scrollPercent * 50}px)`;
        bgElement.style.opacity = 1 - scrollPercent * 0.5;
      }
    }
  }

  // Lazy Loading Setup
  function setupLazyLoading() {
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src || img.src;
            img.classList.add('loaded');
            observer.unobserve(img);
          }
        });
      });

      lazyImages.forEach(img => imageObserver.observe(img));
    } else {
      // Fallback for browsers without IntersectionObserver
      lazyImages.forEach(img => {
        img.src = img.dataset.src || img.src;
        img.classList.add('loaded');
      });
    }
  }

  // Music Player Setup
  function setupMusicPlayer() {
    const musicPlayer = document.getElementById('music-player');
    const musicToggle = document.getElementById('music-toggle');

    if (!musicPlayer || !musicToggle) return;

    musicToggle.addEventListener('click', () => {
      const isOpen = musicPlayer.classList.contains('music-player--open');

      if (isOpen) {
        musicPlayer.classList.remove('music-player--open');
      } else {
        musicPlayer.classList.add('music-player--open');
      }
    });

    // Close music player when clicking outside
    document.addEventListener('click', (e) => {
      if (!musicPlayer.contains(e.target)) {
        musicPlayer.classList.remove('music-player--open');
      }
    });

    // Prevent closing when clicking inside the player
    musicPlayer.addEventListener('click', (e) => {
      e.stopPropagation();
    });
  }

  // Intersection Observer for fade-in animations
  function setupIntersectionObserver() {
    if ('IntersectionObserver' in window) {
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
          }
        });
      }, observerOptions);

      fadeElements.forEach(element => {
        observer.observe(element);
      });
    } else {
      // Fallback: show all elements immediately
      fadeElements.forEach(element => {
        element.classList.add('visible');
      });
    }
  }

  // Testimonial Carousel Setup
  function setupTestimonialCarousel() {
    const testimonials = document.querySelectorAll('.testimonial');
    const dots = document.querySelectorAll('.testimonials__dot');
    let currentTestimonial = 0;

    if (testimonials.length === 0) return;

    // Auto-rotate testimonials
    setInterval(() => {
      showTestimonial((currentTestimonial + 1) % testimonials.length);
    }, 5000);

    // Dot navigation
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        showTestimonial(index);
      });
    });

    function showTestimonial(index) {
      // Hide all testimonials
      testimonials.forEach(testimonial => {
        testimonial.classList.remove('testimonial--active');
      });

      // Remove active state from all dots
      dots.forEach(dot => {
        dot.classList.remove('testimonials__dot--active');
      });

      // Show selected testimonial
      if (testimonials[index]) {
        testimonials[index].classList.add('testimonial--active');
      }

      // Activate corresponding dot
      if (dots[index]) {
        dots[index].classList.add('testimonials__dot--active');
      }

      currentTestimonial = index;
    }
  }

  // Hero Slideshow Setup
  function setupHeroSlideshow() {
    const slides = document.querySelectorAll('.slide');
    let currentSlide = 0;
    let slideInterval;

    if (slides.length === 0) return;

    // Auto-advance slides every 10 seconds
    function startSlideshow() {
      slideInterval = setInterval(() => {
        showSlide((currentSlide + 1) % slides.length);
      }, 10000); // 10 seconds
    }

    // Show specific slide
    function showSlide(index) {
      // Remove active class from all slides
      slides.forEach(slide => {
        slide.classList.remove('slide--active', 'slide--entering');
      });

      // Add active class to current slide
      if (slides[index]) {
        slides[index].classList.add('slide--active', 'slide--entering');
      }

      currentSlide = index;
    }

    // Start the slideshow immediately
    startSlideshow();
  }

  // Utility Functions
  function throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  function debounce(func, wait, immediate) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      const later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  }

  // Error handling
  window.addEventListener('error', (e) => {
    console.error('JavaScript error:', e.error);
  });

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();
